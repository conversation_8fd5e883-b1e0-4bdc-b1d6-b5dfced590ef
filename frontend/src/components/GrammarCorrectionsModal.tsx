import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { X, CheckCircle, AlertCircle, BarChart3 } from 'lucide-react';
import { videoApi } from '../utils/api';

interface GrammarCorrectionsModalProps {
  videoId: number;
  isOpen: boolean;
  onClose: () => void;
}

const GrammarCorrectionsModal: React.FC<GrammarCorrectionsModalProps> = ({
  videoId,
  isOpen,
  onClose,
}) => {
  const {
    data: corrections,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['grammar-corrections', videoId],
    queryFn: () => videoApi.getGrammarCorrections(videoId),
    enabled: isOpen && !!videoId,
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">
                  Grammar Corrections
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Content */}
            <div className="mt-4">
              {isLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading corrections...</span>
                </div>
              )}

              {error && (
                <div className="flex items-center justify-center py-8 text-red-600">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  <span>Failed to load grammar corrections</span>
                </div>
              )}

              {corrections && !corrections.grammar_corrected && (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    No Grammar Corrections Applied
                  </h4>
                  <p className="text-gray-600">
                    {corrections.message || 'This video transcript was not processed with grammar correction.'}
                  </p>
                </div>
              )}

              {corrections && corrections.grammar_corrected && (
                <div className="space-y-6">
                  {/* Statistics */}
                  {corrections.statistics && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center mb-3">
                        <BarChart3 className="h-5 w-5 text-blue-500 mr-2" />
                        <h4 className="text-md font-medium text-gray-900">Correction Statistics</h4>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {corrections.statistics.original_word_count}
                          </div>
                          <div className="text-gray-600">Original Words</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {corrections.statistics.corrected_word_count}
                          </div>
                          <div className="text-gray-600">Corrected Words</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {corrections.statistics.character_difference > 0 ? '+' : ''}
                            {corrections.statistics.character_difference}
                          </div>
                          <div className="text-gray-600">Character Diff</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {corrections.language?.toUpperCase() || 'AUTO'}
                          </div>
                          <div className="text-gray-600">Language</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Before and After Comparison */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Original Transcript */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                        <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                        Original Transcript
                      </h4>
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                          {corrections.original_transcript}
                        </pre>
                      </div>
                    </div>

                    {/* Corrected Transcript */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                        <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                        Corrected Transcript
                      </h4>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                          {corrections.corrected_transcript}
                        </pre>
                      </div>
                    </div>
                  </div>

                  {/* Improvement Summary */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-2">
                      ✨ Grammar Correction Summary
                    </h4>
                    <p className="text-blue-800 text-sm">
                      The AI grammar correction system has improved this transcript by fixing spelling, 
                      grammar, and punctuation errors while preserving the original meaning and natural 
                      speaking style. The corrected version is more readable and professional.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GrammarCorrectionsModal;
