from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends, UploadFile, File, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uvicorn
from datetime import datetime

# Import models and dependencies
from models.database import create_tables, get_db
from models.schemas import (
    VideoResponse, TagResponse, AnalyticsResponse, UploadResponse,
    HealthResponse, VideoFilter, PaginationParams, TagCreate, TagUpdate,
    VideoUpdate, ExportFormat, ErrorResponse, DownloadRequest, DownloadResponse
)

# Import routers
from routers import videos, tags, analytics, export, recipes, auth, sharing

# Create FastAPI app
app = FastAPI(
    title="tagTok API",
    description="API for TikTok video organization platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware

# Get allowed origins from environment or use defaults
import os
allowed_origins = os.getenv("CORS_ORIGINS", "https://tt.mvt.ar,http://localhost:3001,http://localhost:8790").split(",")


app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,,  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    create_tables()
    
    # Create necessary directories
    os.makedirs("videos", exist_ok=True)
    os.makedirs("db", exist_ok=True)
    os.makedirs("transcripts", exist_ok=True)

# Include routers
app.include_router(auth.router, prefix="/auth", tags=["authentication"])
app.include_router(sharing.router, prefix="/sharing", tags=["sharing"])
app.include_router(videos.router, prefix="/videos", tags=["videos"])
app.include_router(tags.router, prefix="/tags", tags=["tags"])
app.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
app.include_router(export.router, prefix="/export", tags=["export"])
app.include_router(recipes.router, prefix="/recipes", tags=["recipes"])

# Health check endpoint - lightweight, no DB dependency
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Lightweight health check that doesn't hit the database"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now()
    )

# Simple status endpoint for server load monitoring
@app.get("/status")
async def server_status():
    """Very lightweight status check"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "tagTok API",
        "version": "1.0.0",
        "docs": "/docs"
    }

# Serve video files
@app.get("/videos/file/{filename}")
async def serve_video(filename: str):
    video_path = os.path.join("videos", filename)
    if not os.path.exists(video_path):
        raise HTTPException(status_code=404, detail="Video file not found")
    return FileResponse(video_path)

# Serve thumbnail files
@app.get("/videos/thumbnail/{filename}")
async def serve_thumbnail(filename: str):
    thumbnail_path = os.path.join("videos", "thumbnails", filename)
    if not os.path.exists(thumbnail_path):
        raise HTTPException(status_code=404, detail="Thumbnail not found")
    return FileResponse(thumbnail_path)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            detail="Internal server error",
            error_code="INTERNAL_ERROR"
        ).dict()
    )

if __name__ == "__main__":
    # Get port from environment variable with fallback
    port = int(os.getenv("BACKEND_INTERNAL_PORT", "8000"))

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True
    )
