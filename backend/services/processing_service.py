import asyncio
import os
import subprocess
import tempfile
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import random

from models.database import Video, Tag, ProcessingJob
from datetime import datetime
from services.video_service import VideoService
from services.tag_service import TagService
from services.recipe_service import RecipeService
from utils.ai_utils import Whisper<PERSON>ranscriber, TagGenerator, GrammarCorrector
from utils.recipe_extractor import RecipeExtractor
from utils.video_utils import extract_video_frames

class ProcessingService:
    # Class-level semaphore to limit concurrent processing across all instances
    _processing_semaphore = asyncio.Semaphore(2)  # Max 2 concurrent video processing operations

    def __init__(self, db: Session):
        self.db = db
        self.video_service = VideoService(db)
        self.tag_service = TagService(db)
        self.recipe_service = RecipeService(db)
        self.transcriber = WhisperTranscriber()
        self.tag_generator = TagGenerator()
        self.recipe_extractor = RecipeExtractor()
        self.grammar_corrector = GrammarCorrector()
    
    async def process_video_async(self, video_id: int):
        """Process a video asynchronously (transcription + tagging)"""
        # Use semaphore to limit concurrent processing operations
        async with self._processing_semaphore:
            try:
                # Update status to processing with 0% progress
                self.video_service.update_processing_status(video_id, "processing", progress=0)

                # Create processing job
                job = ProcessingJob(
                    video_id=video_id,
                    job_type="full_processing",
                    status="running",
                    started_at=datetime.now()
                )
                self.db.add(job)
                self.db.commit()

                # Get video
                video = self.video_service.get_video_by_id(video_id)
                if not video:
                    raise Exception(f"Video {video_id} not found")

                # Step 1: Transcribe video (0% -> 50%)
                self.video_service.update_processing_status(video_id, "processing", progress=10)
                print(f"Starting transcription for video {video_id}")

                transcript, language = await self.transcribe_video(video.file_path)

                self.video_service.update_processing_status(video_id, "processing", progress=50)
                print(f"Transcription completed for video {video_id}")

                # Step 2: Correct grammar in transcript (50% -> 70%) - if enabled
                from config import config
                if config.ENABLE_GRAMMAR_CORRECTION:
                    self.video_service.update_processing_status(video_id, "processing", progress=55)
                    print(f"Starting grammar correction for video {video_id}")

                    corrected_transcript = await self.correct_transcript_grammar(transcript, language)

                    self.video_service.update_processing_status(video_id, "processing", progress=70)
                    print(f"Grammar correction completed for video {video_id}")

                    # Store both original and corrected transcripts
                    final_transcript = corrected_transcript
                    grammar_corrected = corrected_transcript != transcript

                    # Update video with original transcript for comparison
                    video = self.video_service.get_video_by_id(video_id)
                    if video:
                        video.original_transcript = transcript
                        video.grammar_corrected = grammar_corrected
                        self.video_service.db.commit()

                    if grammar_corrected:
                        print(f"Grammar corrections applied to video {video_id}")
                    else:
                        print(f"No grammar corrections needed for video {video_id}")
                else:
                    print(f"Grammar correction disabled, using original transcript for video {video_id}")
                    final_transcript = transcript
                    grammar_corrected = False

                    # Store original transcript even when correction is disabled
                    video = self.video_service.get_video_by_id(video_id)
                    if video:
                        video.original_transcript = transcript
                        video.grammar_corrected = False
                        self.video_service.db.commit()

                    self.video_service.update_processing_status(video_id, "processing", progress=70)

                # Step 3: Generate tags from transcript (70% -> 90%)
                self.video_service.update_processing_status(video_id, "processing", progress=75)
                print(f"Starting tag generation for video {video_id}")

                suggested_tags = await self.generate_tags_from_transcript(
                    final_transcript, video.title or video.original_filename
                )

                self.video_service.update_processing_status(video_id, "processing", progress=85)
                print(f"Tag generation completed for video {video_id}")

                # Step 4: Extract recipe if it's a cooking video (85% -> 95%)
                self.video_service.update_processing_status(video_id, "processing", progress=87)
                print(f"Starting recipe extraction for video {video_id}")

                recipe_create = await self.recipe_extractor.extract_recipe(
                    final_transcript, video.title or video.original_filename, video_id
                )

                if recipe_create:
                    try:
                        recipe = self.recipe_service.create_recipe(recipe_create)
                        print(f"Recipe extracted and saved for video {video_id} with confidence {recipe_create.extraction_confidence}")
                    except Exception as e:
                        print(f"Failed to save recipe for video {video_id}: {e}")
                else:
                    print(f"No recipe detected for video {video_id}")

                # Step 5: Save tags and finalize (95% -> 100%)
                self.video_service.update_processing_status(video_id, "processing", progress=95)

                # Clear existing tags first to avoid duplicates during reprocessing
                video = self.video_service.get_video_by_id(video_id)
                if video and video.tags:
                    print(f"Clearing {len(video.tags)} existing tags for video {video_id}")
                    for existing_tag in video.tags[:]:  # Create a copy to avoid modification during iteration
                        self.tag_service.remove_tag_from_video(existing_tag.id, video_id)

                # Create and assign new tags
                print(f"Adding {len(suggested_tags)} new tags for video {video_id}")
                for tag_data in suggested_tags:
                    # Check if tag exists
                    existing_tag = self.tag_service.get_tag_by_name(tag_data["name"])
                    if not existing_tag:
                        # Create new tag
                        from models.schemas import TagCreate
                        tag_create = TagCreate(
                            name=tag_data["name"],
                            color=tag_data["color"],
                            description=tag_data["description"]
                        )
                        tag = self.tag_service.create_tag(tag_create)
                    else:
                        tag = existing_tag

                    # Add tag to video (only if not already associated)
                    if not self.tag_service.is_tag_associated_with_video(tag.id, video_id):
                        self.tag_service.add_tag_to_video(tag.id, video_id)
                    else:
                        print(f"Tag '{tag.name}' already associated with video {video_id}, skipping")

                # Update video with corrected transcript and status (100% complete)
                self.video_service.update_processing_status(
                    video_id, "completed", final_transcript, language, progress=100
                )

                # Update job status
                job.status = "completed"
                job.completed_at = datetime.now()
                self.db.commit()

                print(f"Video {video_id} processing completed successfully")

            except Exception as e:
                print(f"Error processing video {video_id}: {e}")

                # Update status to failed
                self.video_service.update_processing_status(video_id, "failed")

                # Update job status
                if 'job' in locals():
                    job.status = "failed"
                    job.error_message = str(e)
                    self.db.commit()
    
    async def transcribe_video(self, video_path: str) -> tuple[str, str]:
        """Transcribe video using Whisper"""
        try:
            transcript, language = await self.transcriber.transcribe(video_path)
            return transcript, language
        except Exception as e:
            print(f"Transcription failed for {video_path}: {e}")
            return "", "unknown"

    async def correct_transcript_grammar(self, transcript: str, language: str) -> str:
        """Correct grammar in transcript using LLM"""
        try:
            if not transcript or len(transcript.strip()) < 10:
                return transcript

            corrected_transcript = await self.grammar_corrector.correct_transcript(transcript, language)

            # Log the improvement for debugging
            if corrected_transcript != transcript:
                print(f"Grammar correction applied. Original length: {len(transcript)}, Corrected length: {len(corrected_transcript)}")
                print(f"Original: {transcript[:100]}...")
                print(f"Corrected: {corrected_transcript[:100]}...")
            else:
                print("No grammar corrections needed or correction failed")

            return corrected_transcript
        except Exception as e:
            print(f"Grammar correction failed: {e}")
            return transcript  # Return original if correction fails
    
    async def generate_tags_from_transcript(
        self, 
        transcript: str, 
        title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate tags from transcript and title"""
        try:
            # Combine transcript and title for analysis
            text_content = f"{title}\n\n{transcript}" if title else transcript
            
            if not text_content.strip():
                return []
            
            # Generate tags using AI
            tags = await self.tag_generator.generate_tags(text_content)
            return tags
        except Exception as e:
            print(f"Tag generation failed: {e}")
            return []
    
    def reprocess_video(self, video_id: int):
        """Queue video for reprocessing"""
        print(f"Starting reprocessing for video {video_id}")

        # Reset processing status
        self.video_service.update_processing_status(video_id, "pending")

        # Clear existing tags to avoid duplicates
        video = self.video_service.get_video_by_id(video_id)
        if video and video.tags:
            print(f"Clearing {len(video.tags)} existing tags for reprocessing video {video_id}")
            for tag in video.tags[:]:  # Create a copy to avoid modification during iteration
                self.tag_service.remove_tag_from_video(tag.id, video_id)

        # Clear existing recipes if any
        try:
            from services.recipe_service import RecipeService
            recipe_service = RecipeService(self.db)
            existing_recipe = recipe_service.get_recipe_by_video_id(video_id)
            if existing_recipe:
                print(f"Removing existing recipe for video {video_id}")
                recipe_service.delete_recipe(existing_recipe.id)
        except Exception as e:
            print(f"Error clearing existing recipe for video {video_id}: {e}")

        # Queue for processing
        asyncio.create_task(self.process_video_async(video_id))
    
    def get_processing_queue_status(self) -> Dict[str, Any]:
        """Get status of processing queue"""
        pending_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "pending"
        ).count()
        
        running_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "running"
        ).count()
        
        completed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "completed"
        ).count()
        
        failed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "failed"
        ).count()
        
        return {
            "pending": pending_jobs,
            "running": running_jobs,
            "completed": completed_jobs,
            "failed": failed_jobs,
            "total": pending_jobs + running_jobs + completed_jobs + failed_jobs
        }
    
    def cleanup_failed_jobs(self):
        """Clean up failed processing jobs"""
        failed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "failed"
        ).all()
        
        for job in failed_jobs:
            # Reset video status to pending for retry
            self.video_service.update_processing_status(job.video_id, "pending")
            
            # Delete failed job
            self.db.delete(job)
        
        self.db.commit()
    
    def get_video_processing_history(self, video_id: int) -> List[ProcessingJob]:
        """Get processing history for a video"""
        return self.db.query(ProcessingJob).filter(
            ProcessingJob.video_id == video_id
        ).order_by(ProcessingJob.id.desc()).all()
