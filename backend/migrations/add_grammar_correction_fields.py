#!/usr/bin/env python3
"""
Migration script to add grammar correction fields to the videos table.
This adds support for tracking original transcripts and grammar correction status.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from models.database import get_db

def migrate_database():
    """Add grammar correction fields to videos table"""
    db = next(get_db())
    
    try:
        print("🔄 Adding grammar correction fields to videos table...")
        
        # Add original_transcript column
        try:
            db.execute(text("ALTER TABLE videos ADD COLUMN original_transcript TEXT"))
            print("✅ Added original_transcript column")
        except Exception as e:
            if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                print("⚠️  original_transcript column already exists")
            else:
                raise e
        
        # Add grammar_corrected column
        try:
            db.execute(text("ALTER TABLE videos ADD COLUMN grammar_corrected BOOLEAN DEFAULT FALSE"))
            print("✅ Added grammar_corrected column")
        except Exception as e:
            if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                print("⚠️  grammar_corrected column already exists")
            else:
                raise e
        
        # Update existing videos to have grammar_corrected = FALSE
        result = db.execute(text("UPDATE videos SET grammar_corrected = FALSE WHERE grammar_corrected IS NULL"))
        print(f"✅ Updated {result.rowcount} existing videos with grammar_corrected = FALSE")
        
        db.commit()
        print("🎉 Migration completed successfully!")
        
        # Show current schema
        print("\n📋 Current videos table schema:")
        columns = db.execute(text("PRAGMA table_info(videos)")).fetchall()
        for col in columns:
            if 'transcript' in col[1] or 'grammar' in col[1]:
                print(f"   - {col[1]}: {col[2]} (nullable: {not col[3]})")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        db.rollback()
        raise e
    finally:
        db.close()

if __name__ == "__main__":
    migrate_database()
